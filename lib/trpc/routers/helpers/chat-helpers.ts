import {
	chats,
	chatParticipants,
	cats,
	users,
	messages,
} from "@/lib/db/schema";
import { eq, and, not, inArray, count } from "drizzle-orm";
import { logSlowQuery } from "./cat-helpers";

// Reusable database query helpers for chat operations
export const chatHelpers = {
	// Check if user is participant in a chat
	async isUserParticipant(db: any, chatId: number, userId: number) {
		const startTime = performance.now();
		const participant = await db.query.chatParticipants.findFirst({
			where: and(
				eq(chatParticipants.chatId, chatId),
				eq(chatParticipants.userId, userId)
			),
		});
		const duration = performance.now() - startTime;
		logSlowQuery("isUserParticipant", duration);
		return !!participant;
	},

	// Get user's chat IDs efficiently
	async getUserChatIds(
		db: any,
		userId: number,
		limit?: number,
		offset?: number
	) {
		return await db
			.select({ chatId: chatParticipants.chatId })
			.from(chatParticipants)
			.where(eq(chatParticipants.userId, userId))
			.limit(limit || 50)
			.offset(offset || 0);
	},

	// Check if conversation exists between user and cat owner
	async findExistingChatForCat(db: any, catId: number, userId: number) {
		const startTime = performance.now();
		const chat = await db.query.chats.findFirst({
			where: eq(chats.catId, catId),
			with: {
				participants: {
					where: eq(chatParticipants.userId, userId),
				},
			},
		});
		const duration = performance.now() - startTime;
		logSlowQuery("findExistingChatForCat", duration);

		return chat && chat.participants.length > 0 ? chat : null;
	},

	// Get basic user info for chat participants
	async getChatParticipants(
		db: any,
		chatIds: number[],
		excludeUserId: number
	) {
		return await db.query.chatParticipants.findMany({
			where: and(
				inArray(chatParticipants.chatId, chatIds),
				not(eq(chatParticipants.userId, excludeUserId))
			),
			with: {
				user: {
					columns: {
						id: true,
						slug: true,
						name: true,
						image: true,
						role: true,
					},
				},
			},
		});
	},

	// Get cat owner info for a specific cat
	async getCatOwnerInfo(db: any, catId: number) {
		const startTime = performance.now();
		const cat = await db.query.cats.findFirst({
			where: eq(cats.id, catId),
			columns: {
				userId: true,
			},
			with: {
				user: {
					columns: {
						id: true,
						slug: true,
						name: true,
						image: true,
					},
				},
			},
		});
		const duration = performance.now() - startTime;
		logSlowQuery("getCatOwnerInfo", duration);

		return cat?.user || null;
	},

	// Check if user exists by ID
	async getUserById(db: any, userId: number) {
		return await db.query.users.findFirst({
			where: eq(users.id, userId),
			columns: {
				id: true,
				name: true,
				email: true,
				slug: true,
				role: true,
				image: true,
			},
		});
	},

	// Get chat with basic info (without heavy joins)
	async getChatBasicInfo(db: any, chatId: number) {
		return await db.query.chats.findFirst({
			where: eq(chats.id, chatId),
			columns: {
				id: true,
				catId: true,
				createdAt: true,
			},
		});
	},

	// Check if chat exists and user has access
	async validateChatAccess(db: any, chatId: number, userId: number) {
		const chat = await this.getChatBasicInfo(db, chatId);
		if (!chat) {
			return { valid: false, error: "Chat not found" };
		}

		const isParticipant = await this.isUserParticipant(db, chatId, userId);
		if (!isParticipant) {
			return {
				valid: false,
				error: "You are not a participant in this chat",
			};
		}

		return { valid: true, chat };
	},

	// Get all participants of a chat
	async getAllChatParticipants(db: any, chatId: number) {
		return await db.query.chatParticipants.findMany({
			where: eq(chatParticipants.chatId, chatId),
			with: {
				user: {
					columns: {
						id: true,
						slug: true,
						name: true,
						image: true,
						role: true,
					},
				},
			},
		});
	},

	// Count total messages in a chat
	async getChatMessageCount(db: any, chatId: number) {
		const result = await db
			.select({ count: count() })
			.from(messages)
			.where(eq(messages.chatId, chatId));

		return result[0]?.count || 0;
	},

	// Check if user has any chats
	async userHasChats(db: any, userId: number) {
		const result = await db
			.select({ count: count() })
			.from(chatParticipants)
			.where(eq(chatParticipants.userId, userId));

		return (result[0]?.count || 0) > 0;
	},
};
